import * as fs from 'fs';
import * as vscode from 'vscode';
import * as childProcess from 'child_process';
import * as path from 'path';
import { EXTENSION_ID, g_context } from './globalSettings';
import { outputChannel, doCLI, delayMs } from './UtilFuns';

export let g_buildInProgress = false;

export enum DatabaseType {
    CSCOPE = 1,
    CTAGS = 2,
    BOTH = 3
}

export function getCommandPath(command: string): string {
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    const useInternalExecutable = config.get<boolean>('useInternalExecutable');
    if (useInternalExecutable) {
        if (process.platform === 'win32') {
            return path.join(g_context!.extensionPath, `${command}.exe`);
        }
        return path.join(g_context!.extensionPath, command);
    }
    const executablePath = config.get<string>('executablePath');
    if (executablePath) {
        return path.join(executablePath, command);
    }
    return command;
}

export function getWorkspaceRootPath(): string {
    return vscode.workspace.workspaceFolders !== undefined ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
}

export function getDatabasePath(): { cscopesDbPath: string; ctagsDbPath: string } {
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    const databasePath = path.join(getWorkspaceRootPath() || '', config.get<string>('databasePath') || '');

    if (!fs.existsSync(databasePath)) {
        fs.mkdirSync(databasePath, { recursive: true });
    }

    return {
        cscopesDbPath: `${databasePath}/${config.get<string>('cscope_database')}`,
        ctagsDbPath: `${databasePath}/${config.get<string>('ctags_database')}`
    };
}

export async function buildDatabase(buildOption: DatabaseType, macroFile: string = ''): Promise<void> {
    if (g_buildInProgress) {
        return;
    }
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
    }, async (progress) => {
        const { cscopesDbPath, ctagsDbPath } = getDatabasePath();
        const databasePath = path.dirname(cscopesDbPath);
        g_buildInProgress = true;

        // Normalize paths for different platforms
        let normalizedMacroFile = macroFile;
        if (macroFile && process.platform === 'win32') {
            normalizedMacroFile = macroFile.replace(/\\/g, '/');
        }

        // Check if cscope.files exists
        const cscopeFilesPath = path.join(databasePath, 'cscope.files');
        const cscopeFilesExists = fs.existsSync(cscopeFilesPath);

        if (!cscopeFilesExists) {
            // Generate cscope.files if it doesn't exist
            progress.report({ increment: 1, message: "Generating cscope.files..." });
            await generate_cscopefiles(databasePath);
            await delayMs(500);
        }

        if ((buildOption === DatabaseType.CTAGS) || (buildOption === DatabaseType.BOTH)) {
            const command = getCommandPath("ctags");
            progress.report({ increment: 5, message: "Building ctags database..." });

            // Build the command with proper options
            let ctagsCommand = `${command} --fields=+i -no "${ctagsDbPath}"`;

            // Add file list option if cscope.files exists
            if (cscopeFilesExists || fs.existsSync(cscopeFilesPath)) {
                ctagsCommand += ` -L "${cscopeFilesPath}"`;
            } else {
                // Fallback to recursive mode if no file list
                ctagsCommand += ' -R';
            }

            // Add macro file option if provided
                if (normalizedMacroFile) {
                    ctagsCommand += ` -m "${normalizedMacroFile}"`;
                }
                if (vscode.workspace.workspaceFolders) {
                    const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
                    const customMacrosFilePath = path.join(workspacePath, 'custom-macros.h');
                    if (fs.existsSync(customMacrosFilePath)) {
                        ctagsCommand += " -m " + customMacrosFilePath;
                    }
                }

            // Start progress updates
            let currentProgress = 0;
            const progressInterval = setInterval(() => {
                currentProgress += 5;
                if (currentProgress >= 50) {
                    clearInterval(progressInterval);
                } else
                    progress.report({ increment: 5, message: "Building ctags database..." });
            }, 4000);

            try {
                await doCLI(ctagsCommand, true);
                clearInterval(progressInterval);
                progress.report({ increment: 5, message: "Building ctags database done" });
                outputChannel.appendLine(`CtagsDatabase built done: ${ctagsDbPath}`);
                await delayMs(500);
            } catch (error) {
                clearInterval(progressInterval);
                throw error;
            }

        }

        if ((buildOption === DatabaseType.CSCOPE) || (buildOption === DatabaseType.BOTH)) {
            const command = getCommandPath("cscope");
            progress.report({ increment: 5, message: "Building Cscope Database..." });

            // Build the command with proper options
            let cscopeCommand = `${command} -bukf "${cscopesDbPath}"`;

            // Use the file list if it exists
            if (cscopeFilesExists || fs.existsSync(cscopeFilesPath)) {
                cscopeCommand += ` -i "${cscopeFilesPath}"`;
            } else {
                // Fallback to recursive mode if no file list
                cscopeCommand += ' -R';
            }
            // Add macro file option if provided
            if (process.platform !== 'win32') {
                if (normalizedMacroFile) {
                    cscopeCommand += ` -m "${normalizedMacroFile}"`;
                }
                if (vscode.workspace.workspaceFolders) {
                    const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
                    const customMacrosFilePath = path.join(workspacePath, 'custom-macros.h');
                    if (fs.existsSync(customMacrosFilePath)) {
                        cscopeCommand += " -m " + customMacrosFilePath;
                    }
                }
            }
            let currentProgress = 0;
            const progressInterval = setInterval(() => {
                currentProgress += 5;
                if (currentProgress >= 50) {
                    clearInterval(progressInterval);
                } else
                    progress.report({ increment: 5, message: "Building cscope database..." });
            }, 4000);
            try {
                await doCLI(cscopeCommand, false);
                clearInterval(progressInterval);
                outputChannel.appendLine(`CscopeDatabase built done: ${cscopesDbPath}`);
                await delayMs(500);
            } catch (error) {
                clearInterval(progressInterval);
                throw error;
            }
        }

        progress.report({ increment: 100, message: "Finished building database" });
        await delayMs(1500);
        g_buildInProgress = false;
    });
}

/**
 * Generate cscope.files in the specified database path.
 * Uses the 'find' command to locate all C, C++, and header files in the workspace.
 * @param dbPath The path where cscope.files should be created
 */
export async function generate_cscopefiles(dbPath: string): Promise<void> {
    try {
        const workspacePath = getWorkspaceRootPath();
        if (!workspacePath) {
            throw new Error('No workspace folder is open');
        }

        // If dbPath is not absolute, make it absolute relative to workspace
        const absoluteDbPath = path.isAbsolute(dbPath) ? dbPath : path.join(workspacePath, dbPath);

        outputChannel.appendLine(`Generating cscope.files in: ${absoluteDbPath}`);

        // Ensure the database directory exists
        if (!fs.existsSync(absoluteDbPath)) {
            fs.mkdirSync(absoluteDbPath, { recursive: true });
        }

        // Path to cscope.files
        const cscopeFilesPath = path.join(absoluteDbPath, 'cscope.files');

        // Get excluded paths from configuration
        const config = vscode.workspace.getConfiguration(EXTENSION_ID);
        const excludedPaths = config.get<string[]>('excludedPaths') || [];

        // Create exclude arguments for the find command
        let excludeArgs = '';
        if (excludedPaths.length > 0) {
            excludeArgs = excludedPaths.map(pattern => {
                // Convert pattern to a format suitable for find's -path argument
                // Escape special characters in the pattern
                const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                return `-path "${escapedPattern}" -prune -o`;
            }).join(' ');
        }

        // Construct the find command based on the platform
        let findCommand: string;

        if (process.platform === 'win32') {
            // For Windows, we need to use PowerShell to execute a more complex command
            // that mimics the find command's functionality
            findCommand = `powershell -Command "Get-ChildItem -Path '${workspacePath}' -Recurse -File | Where-Object { $_.Extension -match '\\.c$|\\.cpp$|\\.cc$|\\.h$|\\.hpp$|\\.hxx$|\\.S$' } | ForEach-Object { $_.FullName } | Out-File -FilePath '${cscopeFilesPath}' -Encoding utf8"`;
        } else {
            // For Unix-like systems, use the find command directly
            findCommand = `find "${workspacePath}" ${excludeArgs} \\( -name "*.c" -o -name "*.cpp" -o -name "*.cc" -o -name "*.h" -o -name "*.hpp" -o -name "*.hxx" -o -name "*.S" \\) -type f -print > "${cscopeFilesPath}"`;
        }

        // Execute the command
        outputChannel.appendLine(`Generating cscope.files with command: ${findCommand}`);
        await doCLI(findCommand, true);

        // Verify the file was created
        if (!fs.existsSync(cscopeFilesPath)) {
            throw new Error(`Failed to create cscope.files at ${cscopeFilesPath}`);
        }

        // Count the number of files found
        const fileContent = fs.readFileSync(cscopeFilesPath, 'utf8');
        const fileCount = fileContent.split('\n').filter(line => line.trim().length > 0).length;

        outputChannel.appendLine(`Generated cscope.files with ${fileCount} source files at ${cscopeFilesPath}`);
        vscode.window.showInformationMessage(`Generated cscope.files with ${fileCount} source files`);

    } catch (error) {
        outputChannel.appendLine(`Error generating cscope.files: ${error instanceof Error ? error.message : String(error)}`);
        throw new Error(`Failed to generate cscope.files: ${error instanceof Error ? error.message : String(error)}`);
    }
}

export async function showBuildDialog(): Promise<void> {
    try {
        // Create a WebviewPanel
        const panel = vscode.window.createWebviewPanel(
            'sourceseekBuildDatabase',
            'Build Database',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // Get default paths and settings
        const config = vscode.workspace.getConfiguration(EXTENSION_ID);
        const workspacePath = getWorkspaceRootPath();
        const { cscopesDbPath } = getDatabasePath();
        const databasePath = path.dirname(cscopesDbPath);
        const savedMacroPath = config.get<string>('macroDefinitionFilePath') || '';

        // Create HTML content for the webview
        panel.webview.html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Build Database</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                }
                .container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .input-group {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                }
                .input-row {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }
                label {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                input[type="text"] {
                    flex-grow: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 2px;
                }
                button {
                    padding: 8px 12px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    cursor: pointer;
                    border-radius: 2px;
                }
                button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .build-button {
                    margin-top: 20px;
                    align-self: flex-start;
                    font-weight: bold;
                    padding: 10px 20px;
                }
                .checkbox-group {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    margin-top: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h2>Build Database Configuration</h2>

                <div class="input-group">
                    <label for="macroDefinitionFile">Macro Definition File:</label>
                    <div class="input-row">
                        <input type="text" id="macroDefinitionFile" value="${savedMacroPath}" placeholder="Path to macro definition file">
                        <button id="browseMacroFile">Browse...</button>
                    </div>
                </div>

                <div class="input-group">
                    <label for="databasePath">Database Path:</label>
                    <div class="input-row">
                        <input type="text" id="databasePath" value="${databasePath}" placeholder="Path to store database files">
                        <button id="browseDatabasePath">Browse...</button>
                    </div>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="buildCscope" checked>
                    <label for="buildCscope">Build Cscope Database</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" id="buildCtags" checked>
                    <label for="buildCtags">Build CTags Database</label>
                </div>

                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button id="generateFilesButton">Generate cscope.files</button>
                    <button id="buildButton" class="build-button">Build Database</button>
                </div>
            </div>

            <script>
                (function() {
                    const vscode = acquireVsCodeApi();

                    // Get elements
                    const macroFileInput = document.getElementById('macroDefinitionFile');
                    const databasePathInput = document.getElementById('databasePath');
                    const browseMacroButton = document.getElementById('browseMacroFile');
                    const browseDatabaseButton = document.getElementById('browseDatabasePath');
                    const buildButton = document.getElementById('buildButton');
                    const generateFilesButton = document.getElementById('generateFilesButton');
                    const buildCscopeCheckbox = document.getElementById('buildCscope');
                    const buildCtagsCheckbox = document.getElementById('buildCtags');

                    // Add event listeners
                    browseMacroButton.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'browseMacroFile',
                            currentPath: macroFileInput.value
                        });
                    });

                    browseDatabaseButton.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'browseDatabasePath',
                            currentPath: databasePathInput.value
                        });
                    });

                    // Generate cscope.files button
                    generateFilesButton.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'generateCscopeFiles',
                            databasePath: databasePathInput.value
                        });
                    });

                    // Build database button
                    buildButton.addEventListener('click', () => {
                        const buildType = (buildCscopeCheckbox.checked && buildCtagsCheckbox.checked) ? 3 :
                                         (buildCscopeCheckbox.checked) ? 1 :
                                         (buildCtagsCheckbox.checked) ? 2 : 0;

                        if (buildType === 0) {
                            vscode.postMessage({
                                command: 'showError',
                                message: 'Please select at least one database type to build'
                            });
                            return;
                        }

                        vscode.postMessage({
                            command: 'buildDatabase',
                            macroFile: macroFileInput.value,
                            databasePath: databasePathInput.value,
                            buildType: buildType
                        });
                    });

                    // Handle messages from the extension
                    window.addEventListener('message', event => {
                        const message = event.data;

                        switch (message.command) {
                            case 'updateMacroFile':
                                macroFileInput.value = message.path;
                                break;

                            case 'updateDatabasePath':
                                databasePathInput.value = message.path;
                                break;

                            case 'initialize':
                                macroFileInput.value = message.macroFile || '';
                                databasePathInput.value = message.databasePath || '';
                                break;
                        }
                    });

                    // Signal that the webview is ready
                    vscode.postMessage({ command: 'webviewReady' });
                })();
            </script>
        </body>
        </html>`;

        // Handle messages from the webview
        panel.webview.onDidReceiveMessage(async message => {
            switch (message.command) {
                case 'browseMacroFile':
                    const macroFileUri = await vscode.window.showOpenDialog({
                        canSelectFiles: true,
                        canSelectFolders: false,
                        canSelectMany: false,
                        openLabel: 'Select Macro Definition File',
                        filters: {
                            'Header Files': ['h'],
                            'All Files': ['*']
                        },
                        defaultUri: message.currentPath ? vscode.Uri.file(message.currentPath) : vscode.Uri.file(workspacePath)
                    });

                    if (macroFileUri && macroFileUri.length > 0) {
                        panel.webview.postMessage({
                            command: 'updateMacroFile',
                            path: macroFileUri[0].fsPath
                        });

                        // Save to configuration
                        await config.update('macroDefinitionFilePath', macroFileUri[0].fsPath, vscode.ConfigurationTarget.Workspace);
                    }
                    break;

                case 'browseDatabasePath':
                    const databasePathUri = await vscode.window.showOpenDialog({
                        canSelectFiles: false,
                        canSelectFolders: true,
                        canSelectMany: false,
                        openLabel: 'Select Database Path',
                        defaultUri: message.currentPath ? vscode.Uri.file(message.currentPath) : vscode.Uri.file(workspacePath)
                    });

                    if (databasePathUri && databasePathUri.length > 0) {
                        panel.webview.postMessage({
                            command: 'updateDatabasePath',
                            path: databasePathUri[0].fsPath
                        });

                        // Save to configuration
                        await config.update('databasePath', path.relative(workspacePath, databasePathUri[0].fsPath), vscode.ConfigurationTarget.Workspace);
                    }
                    break;

                case 'buildDatabase':
                    // Close the panel
                    panel.dispose();

                    // Save the macro file path to configuration
                    if (message.macroFile) {
                        await config.update('macroDefinitionFilePath', message.macroFile, vscode.ConfigurationTarget.Workspace);
                    }

                    // Save the database path to configuration
                    if (message.databasePath && message.databasePath !== databasePath) {
                        const relativePath = path.relative(workspacePath, message.databasePath);
                        await config.update('databasePath', relativePath, vscode.ConfigurationTarget.Workspace);
                    }

                    try {
                        // No need to explicitly generate cscope.files first as buildDatabase will do it if needed

                        // Build the database
                        await buildDatabase(message.buildType, message.macroFile);
                    } catch (error) {
                        vscode.window.showErrorMessage(`Error building database: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    break;

                case 'generateCscopeFiles':
                    try {
                        // Save the database path to configuration if it's different
                        if (message.databasePath && message.databasePath !== databasePath) {
                            const relativePath = path.relative(workspacePath, message.databasePath);
                            await config.update('databasePath', relativePath, vscode.ConfigurationTarget.Workspace);
                        }

                        // Generate cscope.files
                        await generate_cscopefiles(message.databasePath);
                    } catch (error) {
                        vscode.window.showErrorMessage(`Error generating cscope.files: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    break;

                case 'showError':
                    vscode.window.showErrorMessage(message.message);
                    break;
            }
        });

        // Update the webview content when it's ready
        panel.webview.onDidReceiveMessage(message => {
            if (message.command === 'webviewReady') {
                panel.webview.postMessage({
                    command: 'initialize',
                    macroFile: savedMacroPath,
                    databasePath: databasePath
                });
            }
        });

    } catch (error) {
        vscode.window.showErrorMessage(`Error showing build dialog: ${error instanceof Error ? error.message : String(error)}`);
    }
}