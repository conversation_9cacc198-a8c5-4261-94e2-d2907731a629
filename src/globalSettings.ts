'use strict';
import * as vscode from 'vscode';

export const EXTENSION_NAME = "SourceSeek";
export const EXTENSION_ID = "sourceseek";
export const TASK_NAME = "rebuild ctags";
export const BROWSER_HISTORY_FILE = "sourceseek-his.json";
export const BROWSER_HISTORY_LIMIT = 50;
export const readtagsGoToSymbolInWorkspaceCommand = "readtags -enpi";
export const readtagsGoToSymbolInWorkspaceCommandWin = "readtags.exe -enpi";
export const readtagsGoToDefinitionCommand = "readtags -en";
export const readtagsGoToDefinitionCommandWin = "readtags.exe -en";
export const ctagsGoToSymbolInEditorCommand = "ctags --fields=+nKz ${macro_file} -f -";
export const ctagsGoToSymbolInEditorCommandWin = "ctags.exe --fields=+nKz ${macro_file} -f -";
export const ctagsGenerateCommand = "ctags --fields=+nkz -R";
export const ctagsGenerateCommandWin = "ctags.exe --fields=+nkz -R";
export const cscopeGenerateCommand = "cscope -b -q -k";
export const cscopeFindAllRefCommand = "cscope -q -k -L0";
export const cscopeFindDefineCommand = "cscope -q -k -L1";
export const cscopeFindCalleeCommand = "cscope -q -k -L2";
export const cscopeFindCallerCommand = "cscope -q -k -L3";
export const cscopeFindTextCommand = "cscope -q -k -L4";
export const linux_find_cmd = "find ${src_path} -type f -name *.c -o -type f -name *.h -o -type f -name *.cpp -o -type f -name *.cc -o -type f -name *.mm";
export const windows_find_cmd = "cmd /C dir /s/a/b ${src_path}\\*.c ${src_path}\\*.h ${src_path}\\*.cpp ${src_path}\\*.cc ${src_path}\\*.mm";
export let g_cscopeupdate = false;
export let g_context: vscode.ExtensionContext|null = null;

export const os_constants = {
    OS_UNKOWN : 0,
    OS_WINDOWS : 1,
    OS_LINUX : 2,
    OS_MAC_OS : 3
};

export interface cmd_result {
    // 命令执行是否成功完成
    success: boolean;
    // 命令返回的状态码
    code: number;
    // 标准输出内容
    stdout: string;
    // 标准错误输出
    stderr: string;
}

export const config_variable_str = {
    SRC_PATH : "${src_path}",
    DATABASE_PATH : "${database_path}",
    SEARCH_TEXT : "${text}",
    WORK_SPACE_PATH : "${workspaceRoot}"
};

export const config_field_str = {
    SCOPE_ENABLE :           "enableCscope",
    EXE_PATH :               "executablePath",
    PRINT_CMD :              "printCmdBeforeExecute",
    ENGINE_CMD_STR :         "engineCommands",
    DATABASE_PATH_STR :      "databasePath",
    OPEN_RESULT_IN_NEW_COL : "openInNewCol",
    SOURCE_CODE_PATHS :      "sourceCodePaths",
    EXCLUDED_PATHS :         "excludedPaths"
};

export const default_config_values = {

}

export function set_context(context: vscode.ExtensionContext):void {
    g_context = context;
}