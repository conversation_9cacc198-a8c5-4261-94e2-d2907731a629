# SourceSeek README

SourceSeek is a tool for browsing C/C++ source code using the database of cscope/ctags/readtags created.
It using the VSCode internerl interface of "Go to Definition", "Go to Reference", "Find All Reference", "Show Call Hierarchy" to navigate the code.

It also provides a outline view to show the code structure of the current file.
It has browser history window to show the browser history of the current project.
It provides a preview window to show the code of the function when the mouse hover on the function name.

It is to replace the VSCode C/C++ extension which is slow for large project like linux kernel.
It uses the database created by cscope/ctags, so don't need to compile the code like the clangd extension.
Other special feature:
1. Call hierarchy export
2. Modified cscope/ctags that can recognize macro definitions, non-active code not be anasysised.
3. Edit windows show non-active code as grey color.
4. Manual link for a symbol and definition, that can be used for funciton pointer.

## Features

### Building the Database

When you open a directory/workspace, the cscope/ctags database will be automatically built on the first search. You can also generate it in advance via command line:

1. cscope -Rcbqk or first create a cscope.files file, then cscope -cbqk
2. ctags --fields=+i -Rno tags

### Outline Window

Displays the function structure in the file, and can toggle to show only functions

![Outline Window](png/sourceseek-outline.PNG)

### Jump to definition(F12)

Go to Definition in context menu or F12 or Ctrl+Click on a symbol
Jumps to the definition, if there are multiple definitions, shows an inline window

![SourceSeek Definition](png/sourceseek-definition.PNG)

### Go to Reference(Shift + F12)

Jumps to references, if there are multiple references, shows an inline window
![Go to Reference](png/sourceseek-reference.png)

### Call Hierarchy Window(Shift+Alt+H)

show the call hierarchy of the current function

![Call Hierarchy](png/sourceseek-hierarchy.PNG)

### All References Window(Shift+Alt+F12)

show all references of the current symbol

![All References](png/sourceseek-allref.png)

### Preview Window

When mouse hovering over a symbol, displays the definition in the right window
It can also show code in a popup window beside the mouse pointer by changing setting

![Preview Window](png/sourceseek-preview.png)

You can also set to show preview in a hover window. By default, struct and macro will show in this window
![previe](png/sourceseek-preview-tips.png)

### Global Search

Type # followed by a symbol prefix in the top bar to search for symbols across the entire workspace

![Global Search](png/sourceseek-search.png)

### Text Search

Double click on a word in edit window then right click to popup context menu and choose Search.

![Text Search](png/sourceseek-search-word.png)
![Text Search](png/sourceseek-search-window.png)

### Browser History Record Window

The history window records the history of symbols searched for definition jumps, Call Hierarchy, and references. It automatically saves in the workspace directory and loads the history record when reopened.
You can also save to a file and load from a file using buttons.
You can filter to show only functions.

![History Window](png/sourceseek-history.png)

For symbol definitions, if a symbol has multiple definitions, they will be displayed in child nodes.

![Multiple Definitions](png/sourceseek-multipledef.png)

For symbol call chains, the CALLERS OF window displays a call chain layer by layer, which is recorded in the history as child nodes. A call chain can be copied to the clipboard.

![History Filter](png/sourceseek-his-call.png)
![History Call Chain](png/sourceseek-call-exam.png)

You can copy the call chain to the clipboard and paste it to other places.

![History Filter](png/sourceseek-clipboard.png)
![History Filter](png/sourceseek-paste.png)

When you go to a function, and then continue go to a sub-function and so on, the history will record the calling stack.

### Manually Map Function Pointer to Definition

For some function pointer calls, or multi-layer calls, you want to skip the intermediate calling process and directly call the key function. You can manually establish a function call link.

Click the function name symbol in the definition, and pop up the menu (Link Caller/Mark Definition), click it, and pop up the menu, you can select the caller, or cancel the menu to just mark a definition, for choosing in Link Definition.

![History Filter](png/sourceseek-fnmap-menu.png)

Click the function name symbol in the definition, and pop up the menu (Link Definition/Mark Caller), click it, and pop up the menu, you can select the function name, or cancel the menu to mark a caller, for choosing in Link caller.

![History Filter](png/sourceseek-fnmap-menu2.png)

The menu will also display the function names in the history record and the marked functions in dialog for choosing.

![History Filter](png/sourceseek-fnmap-popup.png)

You can right-click the menu to display the established call relationship

![History Filter](png/sourceseek-fnmap-menuprint.png)

The call relationship will be displayed in the OUTPUT window

![History Filter](png/sourceseek-fnmap-menuprint-output.png)

### Status Bar Quick Settings

Click SourceSeek in the status bar to display several toggle switches

![Status Bar](png/sourceseek-statusbar.png)

A menu for on/off some settings

![Quick Settings](png/sourceseek-statusmenu.png)

1. Enable mouse hover preview
2. Use the same tab to open preview files or open a new tab for new files
3. Whether to add hovered symbols to history
4. Whether to preview in the right window or in hover tips
5. Whether to display cscope/ctags/readtags command line.

### Commands

Windows and Linux versions of cscope/ctags/readtags commands are pre-installed in the extension.
You can configure to use system-provided commands, that need set search path in system environment or specify the path for the commands in settings.

![Command Settings](png/sourceseek-setting1.png)
![Path Settings](png/sourceseek-setting2.png)

## Requirements

It depends on the cscope/ctags/readtags tools.

## Extension Settings

This extension contributes the following settings:

* `sourceseek.enableSourceSeek`: Enable/disable this extension.
* `sourceseek.enableReferenceSearch`: Enable/disable reference search.
* `sourceseek.enableDefinitionSearch`: Enable/disable definition search.
* `sourceseek.enableCallHierarchy`: Enable/disable call hierarchy.
* `sourceseek.enableWorkspaceSymbol`: Enable/disable workspace symbol search.
* `sourceseek.enableDocumentSymbol`: Enable/disable document symbol search.
* `sourceseek.HoverPreviewEnabled`: Enable/disable hover preview.
* `sourceseek.openRightWindow`: Enable/disable open preview in right window.
* `sourceseek.openInNewWindow`: Enable/disable open preview in a new window.
* `sourceseek.hoverAddHistory`: Enable/disable add hovered symbols to history.
* `sourceseek.useInternalExecutable`: Enable/disable use internal executable.
* `sourceseek.executablePath`: Set the path for cscope/ctags/readtags.
* `sourceseek.showCommand`: Enable/disable show cscope/ctags/readtags command line.
* `sourceseek.databasePath`: Set the path for cscope/ctags/readtags database.
* `sourceseek.cscope_database`: Set the name for cscope database.
* `sourceseek.ctags_database`: Set the name for ctags database.
* `sourceseek.excludedPaths`: Set the excluded paths for cscope/ctags/readtags.
* `sourceseek.hoverCallerEnabled`: Enable/disable showing Call Hierarchy on mouse hover. When enabled, hovering a symbol will also trigger the Call Hierarchy view.

## Known Issues

 mouse hover preview with tips is showing well

## Release Notes

### 1.6.4
1. fix ctags/cscope open two many files issue when enable macro
2. hover call hierarchy trigger by hover+click and get focus back

### 1.6.2
1. Add support of "Find file inlcude this file"
2. support include file like <linux.slab.h>

### 1.6.0
1. Add SourceSeek submenu and add hover call hierarchy
2. Fix deadloop when history full
3. Add menu bar sourceseek
4. Add support for file reference and go to file, find assignemnt(cscope -L6 -L7, -L8, -L9)

### 1.5.6
1. if ctags does not find symbol, try to use cscope again
2. generate cscope.files include .S file.

### 1.5.4
1. fix absolution path issue for symbol jump and call hierarchy show
2. fix delete history item issue
3. support macro file for outline in win32

### 1.5.2
1. support ctags -m <macrofile> for win32

### 1.5.0
1. use exuberant-ctags replace universial-ctags for better performance
2. fix ctags/cscope for code like "#if defined FEATURE_XXXXx"
3. build database show progresss better
4. reduce ctags/cscope size

### 1.4.6
1. ctags/csope support IS_ENABLED macro
2. ctags/csope support 2 macro files
3. build database support 2 macro files(custom macro file and autoconf.h)
4. support outline readtags with 2 macro files
5. async for edit macor update 
6. Add option setting for hiarerchy caller whether to add to history
7. limit to caller at max 20
8. add check for call tree maintain.

### 1.4.4
1. Edit show code support IS_ENABLED and defined XXX

### 1.4.2
1. Hover in right windows, already show in tips, not jump

### 1.4.0
1. support custom macro add/delete dialog

### 1.3.0
1. support load a macro definition file and show non-active code as grey color.
2. support toggle enable/disable show non-active code as grey 

### 1.2.0
1. support generate linux kernel cscope.files depend on .cmd file
2. add -m <macro file> support for ctags and cscope,(only linux)
 for linux kernel, the file kernel/include/generated/autoconf.h include the macros, so
 when creating ctags and cscope database, the code that depend on macro definition will not be included.
 e.g.
 #define CONFIG_FEATURE 1
 #ifdef CONFIG_FEATURE

### 1.1.0
1. Add review feteature

### 1.0.0
1. This is no big changes compare with 0.9.5, just a message. So we got a 1.0 version.


### 0.9.5

1. add link in hover window to open in right window
2. bookmark use relative path

### 0.9.4

1. show path in history item tips
2. use function+line for bookmark

### 0.9.3

1. Add F2 key for bookmark
2. mark caller support without in function

### 0.9.2

1. Add open in right window menu for history item
2. Add open in right window menu for edit window
3. Add file path in hover window

### 0.9.1

1. For go to definitions, filter function, struct, macro, and add all to history, except function.
2. Fix hover tips show code format issue
3. For struct and micro, show in hover tips.

### 0.9.0

1. Add calling stack history feature
2. Move symbol to newest if already in history
3. Identify multiline function definition
4. Support history buffer size setting
5. show called/calling numbers in root item of call hierarchy
6. Identify struct variable from last 30 lines
7. Support Delete whole tree
8. export to clipboard both calling and called
9. Support drag&drop for history symbol
10. Fix delete issue of history symbol

### 0.8.0

1. Add support for function referenced by struct variable in call hierarchy
2. If no function called, return referenced by struct variable for a function
3. Support hover to show function code in tips.
4. Add option for function-struct only in workspace symbol search
5. reference by does not return EXPORT_SYMBOL and declaration
6. Fix load/save history order issue
7. Fix using same tab to show preview file

### 0.7.0

1. Add support for manually set up function pointer to definiton map
2. Add support for editing description for symbol history
3. Add support call hierarchy and go to definition for manually map
4. change symbol history path to relative to workspace
5. fix issue of no symbol history file register fail
6. fix issue of save history with fixed order
7. fix issue of remove one history item

### 0.6.1

1. Add autobuild option
2. try cscope when ctags database is not found for go to definision

### 0.6

1. Add symbol search context menu
2. Add some buttons

### 0.5.2

1. fix linux call hierarchy issue

### 0.5.1

1. fix build issue

### 0.5.0

1. Add setting for execute path
2. Add enable/disable option for features
3. Add save/load button for history window
4. Add clear button for history window
5. Add setting for cscope/ctags/readtags command line.

### 0.4.0

1. Add setting for preview window.

### 0.3.0

1. Add preview window for function definition.

### 0.2.0

1. Add history window for browser history.

### 0.1.0

1. Initial version of SourceSeek.
2. Support cscope and ctags/readtags for C/C++ code search and navigation.
3. Add outline view function switch for code structure.

---

## Following extension guidelines

Ensure that you've read through the extensions guidelines and follow the best practices for creating your extension.

* [Extension Guidelines](https://code.visualstudio.com/api/references/extension-guidelines)

**Enjoy!**
